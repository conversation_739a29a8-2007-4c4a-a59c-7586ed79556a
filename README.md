# 🤖 AI Agent - <PERSON><PERSON> tích đa định dạng với FastAPI & Gemini

Một AI Agent thông minh có khả năng phân tích và trả lời câu hỏi dựa trên nội dung từ các file đa định dạng (Word, Excel, PDF, ảnh, audio, video) sử dụng FastAPI và Google Gemini AI.

## ✨ Tính năng chính

- **📁 Upload đa định dạng**: Hỗ trợ Word, Excel, PDF, ảnh, audio, video
- **🧠 RAG (Retrieval-Augmented Generation)**: Ưu tiên thông tin từ file đã upload
- **🔍 Vector Search**: Tìm kiếm thông tin liên quan bằng ChromaDB
- **💬 Chat thông minh**: Giao diện chat hiện đại với AI Agent
- **📊 Thống kê**: Theo dõi số lượng file và chunks đã xử lý
- **🎨 Giao diện đẹp**: UI/UX hiện đại, responsive

## 🚀 Cài đặt và chạy

### 1. Cài đặt dependencies

```bash
# Cài đặt các thư viện cần thiết
pip install -r requirements.txt
```

### 2. Cấu hình API Key

Tạo file `.env` trong thư mục gốc:

```env
GEMINI_API_KEY=your_gemini_api_key_here
```

### 3. Chạy ứng dụng

```bash
# Chạy server FastAPI
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

Truy cập: http://localhost:8000

## 📋 Hướng dẫn sử dụng

### Bước 1: Upload tài liệu
1. Kéo thả file vào vùng upload hoặc click để chọn file
2. Hệ thống sẽ tự động trích xuất nội dung và lưu vào vector database
3. Xem danh sách file đã upload ở panel bên trái

### Bước 2: Chat với AI
1. Nhập câu hỏi vào ô chat
2. AI sẽ tìm kiếm thông tin liên quan từ file đã upload
3. Nếu không tìm thấy, AI sẽ sử dụng kiến thức tổng quát

### Bước 3: Quản lý file
- Xem danh sách file đã upload
- Xóa file không cần thiết
- Theo dõi thống kê xử lý

## 🔧 Định dạng file hỗ trợ

### 📄 Tài liệu
- **Word**: .docx, .doc
- **PDF**: .pdf
- **Text**: .txt

### 📊 Bảng tính
- **Excel**: .xlsx, .xls
- **CSV**: .csv

### 🖼️ Hình ảnh
- **Ảnh**: .jpg, .jpeg, .png, .gif, .bmp, .tiff
- Sử dụng Gemini Vision để trích xuất text và mô tả

### 🎵 Audio
- **Audio**: .mp3, .wav, .m4a, .flac
- Chuyển đổi speech-to-text bằng Google Speech Recognition

### 🎬 Video
- **Video**: .mp4, .avi, .mov, .mkv, .wmv
- Trích xuất audio + phân tích key frames

## 🏗️ Kiến trúc hệ thống

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   File Upload   │───▶│ Content Extract │───▶│  Vector Store   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
┌─────────────────┐    ┌─────────────────┐             │
│   User Query    │───▶│   RAG Search    │◀────────────┘
└─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │  Gemini AI      │
                       │  Response       │
                       └─────────────────┘
```

## 📁 Cấu trúc project

```
FastAPI_Gemini/
├── main.py                 # FastAPI app chính
├── gemini_agent.py         # RAG Agent với Gemini
├── file_manager.py         # Quản lý upload/metadata
├── content_extractor.py    # Trích xuất nội dung
├── vector_store.py         # ChromaDB vector store
├── requirements.txt        # Dependencies
├── .env                    # API keys
├── templates/
│   └── home.html          # Giao diện web
├── static/
│   └── style.css          # CSS styling
├── uploads/               # Thư mục lưu file
├── chroma_db/            # Vector database
└── file_metadata.json    # Metadata file
```

## 🔧 API Endpoints

### Upload & File Management
- `POST /upload` - Upload file mới
- `GET /files` - Lấy danh sách file
- `DELETE /files/{file_id}` - Xóa file

### Chat & AI
- `POST /chat` - Chat với AI (JSON response)
- `POST /home` - Chat với AI (HTML response)

### System
- `GET /stats` - Thống kê hệ thống
- `GET /health` - Health check

## 🛠️ Troubleshooting

### Lỗi thường gặp

1. **Import Error**: Chưa cài đặt dependencies
   ```bash
   pip install -r requirements.txt
   ```

2. **API Key Error**: Chưa cấu hình GEMINI_API_KEY
   ```bash
   # Tạo file .env với API key
   echo "GEMINI_API_KEY=your_key_here" > .env
   ```

3. **File Upload Error**: Kiểm tra định dạng file hỗ trợ

4. **Memory Error**: File quá lớn, giảm kích thước hoặc tăng RAM

### Performance Tips

- **Chunk Size**: Điều chỉnh chunk_size trong vector_store.py
- **Embedding Model**: Thay đổi model trong vector_store.py
- **File Size Limit**: Điều chỉnh trong main.py (hiện tại: 50MB)

## 🤝 Đóng góp

1. Fork project
2. Tạo feature branch
3. Commit changes
4. Push to branch
5. Create Pull Request

## 📄 License

MIT License - xem file LICENSE để biết thêm chi tiết.

## 🙏 Acknowledgments

- **Google Gemini AI** - AI model chính
- **ChromaDB** - Vector database
- **FastAPI** - Web framework
- **Sentence Transformers** - Text embeddings
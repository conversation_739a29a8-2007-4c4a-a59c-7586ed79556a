<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>🤖 AI Agent - <PERSON><PERSON> tích đa định dạng</title>
  <link rel="stylesheet" href="/static/style.css">
</head>
<body>
  <div class="container">
    <!-- Header -->
    <header class="header">
      <h1>🤖 AI Agent - Phân tích đa định dạng</h1>
      <p>Upload file và chat với AI dựa trên nội dung tài liệu của bạn</p>
    </header>

    <!-- Main Content -->
    <div class="main-content">
      <!-- Left Panel: File Upload & Management -->
      <div class="left-panel">
        <div class="upload-section">
          <h3>📁 Upload Tài liệu</h3>
          <div class="upload-area" id="uploadArea">
            <input type="file" id="fileInput" accept=".docx,.doc,.pdf,.txt,.xlsx,.xls,.csv,.jpg,.jpeg,.png,.gif,.bmp,.tiff,.mp3,.wav,.m4a,.flac,.mp4,.avi,.mov,.mkv,.wmv" multiple>
            <div class="upload-text">
              <p>🔄 Kéo thả file hoặc click để chọn</p>
              <small>Hỗ trợ: Word, Excel, PDF, Ảnh, Audio, Video</small>
            </div>
          </div>
          <div id="uploadStatus"></div>
        </div>

        <!-- File List -->
        <div class="file-list-section">
          <h3>📋 Tài liệu đã upload</h3>
          <div class="file-list" id="fileList">
            {% if uploaded_files %}
              {% for file in uploaded_files %}
                <div class="file-item" data-file-id="{{ file.id }}">
                  <div class="file-info">
                    <span class="file-name">{{ file.original_name }}</span>
                    <span class="file-type">{{ file.file_type }}</span>
                    <span class="file-status">
                      {% if file.processed %}
                        ✅ Đã xử lý
                      {% else %}
                        ⏳ Đang xử lý
                      {% endif %}
                    </span>
                  </div>
                  <button class="delete-btn" onclick="deleteFile('{{ file.id }}')">🗑️</button>
                </div>
              {% endfor %}
            {% else %}
              <p class="no-files">Chưa có tài liệu nào được upload</p>
            {% endif %}
          </div>
        </div>

        <!-- Statistics -->
        <div class="stats-section">
          <h3>📊 Thống kê</h3>
          <div class="stats">
            {% if vector_stats %}
              <div class="stat-item">
                <span class="stat-label">Tổng chunks:</span>
                <span class="stat-value">{{ vector_stats.total_chunks or 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Tài liệu unique:</span>
                <span class="stat-value">{{ vector_stats.unique_files or 0 }}</span>
              </div>
            {% endif %}
          </div>
        </div>
      </div>

      <!-- Right Panel: Chat -->
      <div class="right-panel">
        <div class="chat-container">
          <h3>💬 Chat với AI Agent</h3>

          <div class="chat-box" id="chatBox">
            {% for msg in messages %}
              <div class="message {{ msg.sender }}">
                <div class="message-content">
                  <strong>{{ "Bạn" if msg.sender == "user" else "AI Agent" }}:</strong>
                  <div class="message-text">{{ msg.text | safe }}</div>
                </div>
              </div>
            {% endfor %}
          </div>

          <form method="post" class="chat-form" id="chatForm">
            <div class="input-group">
              <input name="user_input" id="userInput" placeholder="Hỏi AI về nội dung tài liệu..." required />
              <button type="submit" id="sendBtn">Gửi</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <script>
    // File upload handling
    const fileInput = document.getElementById('fileInput');
    const uploadArea = document.getElementById('uploadArea');
    const uploadStatus = document.getElementById('uploadStatus');

    // Drag and drop functionality
    uploadArea.addEventListener('dragover', (e) => {
      e.preventDefault();
      uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', () => {
      uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', (e) => {
      e.preventDefault();
      uploadArea.classList.remove('dragover');
      const files = e.dataTransfer.files;
      handleFiles(files);
    });

    uploadArea.addEventListener('click', () => {
      fileInput.click();
    });

    fileInput.addEventListener('change', (e) => {
      handleFiles(e.target.files);
    });

    async function handleFiles(files) {
      for (let file of files) {
        await uploadFile(file);
      }
    }

    async function uploadFile(file) {
      const formData = new FormData();
      formData.append('file', file);

      try {
        uploadStatus.innerHTML = `<p class="uploading">📤 Đang upload ${file.name}...</p>`;

        const response = await fetch('/upload', {
          method: 'POST',
          body: formData
        });

        const result = await response.json();

        if (result.success) {
          uploadStatus.innerHTML = `<p class="success">✅ ${result.message}</p>`;
          setTimeout(() => location.reload(), 1000);
        } else {
          uploadStatus.innerHTML = `<p class="error">❌ ${result.message}</p>`;
        }
      } catch (error) {
        uploadStatus.innerHTML = `<p class="error">❌ Lỗi upload: ${error.message}</p>`;
      }
    }

    // Delete file function
    async function deleteFile(fileId) {
      if (!confirm('Bạn có chắc muốn xóa file này?')) return;

      try {
        const response = await fetch(`/files/${fileId}`, {
          method: 'DELETE'
        });

        const result = await response.json();

        if (result.success) {
          location.reload();
        } else {
          alert('Lỗi xóa file: ' + result.message);
        }
      } catch (error) {
        alert('Lỗi xóa file: ' + error.message);
      }
    }

    // Auto-scroll chat box
    const chatBox = document.getElementById('chatBox');
    if (chatBox) {
      chatBox.scrollTop = chatBox.scrollHeight;
    }
  </script>
</body>
</html>

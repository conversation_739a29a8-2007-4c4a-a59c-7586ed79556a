import google.generativeai as genai
import os
import logging
from typing import List, Dict, Optional
from dotenv import load_dotenv
from vector_store import VectorStore

load_dotenv()
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GeminiRAGAgent:
    def __init__(self, api_key: Optional[str] = None):
        """Initialize Gemini RAG Agent with vector store integration"""
        self.api_key = api_key or os.getenv("GEMINI_API_KEY")
        if not self.api_key:
            raise ValueError("GEMINI_API_KEY not found in environment variables")

        genai.configure(api_key=self.api_key)
        self.model = genai.GenerativeModel("models/gemini-2.0-flash")

        # Initialize vector store
        self.vector_store = VectorStore()

        # System prompt for RAG
        self.system_prompt = """Bạ<PERSON> là một AI assistant thông minh có khả năng phân tích và trả lời câu hỏi dựa trên:
1. Thông tin từ các file tài liệu đã được cung cấp (ưu tiên cao nhất)
2. <PERSON><PERSON><PERSON> thức tổng quát của bạn (nếu không có thông tin trong tài liệu)

Khi trả lời:
- Ưu tiên sử dụng thông tin từ tài liệu đã cung cấp
- Nếu thông tin trong tài liệu không đủ, hãy kết hợp với kiến thức của bạn
- Luôn chỉ rõ nguồn thông tin (từ tài liệu hay kiến thức tổng quát)
- Trả lời bằng tiếng Việt một cách tự nhiên và dễ hiểu
- Nếu không tìm thấy thông tin liên quan, hãy thông báo rõ ràng"""

    def search_relevant_content(self, query: str, n_results: int = 5) -> List[Dict]:
        """Search for relevant content from uploaded documents"""
        try:
            results = self.vector_store.search_similar(query, n_results=n_results)
            logger.info(f"Found {len(results)} relevant documents for query: {query[:50]}...")
            return results
        except Exception as e:
            logger.error(f"Error searching content: {str(e)}")
            return []

    def format_context(self, search_results: List[Dict]) -> str:
        """Format search results into context for the prompt"""
        if not search_results:
            return ""

        context_parts = ["=== THÔNG TIN TỪ TÀI LIỆU ==="]

        for i, result in enumerate(search_results, 1):
            metadata = result.get("metadata", {})
            content = result.get("content", "")
            similarity = result.get("similarity_score", 0)

            # Only include results with reasonable similarity
            if similarity > 0.3:
                file_name = metadata.get("original_name", "Unknown file")
                file_type = metadata.get("file_type", "unknown")

                context_parts.append(f"\n[Tài liệu {i}: {file_name} ({file_type})]")
                context_parts.append(f"Độ liên quan: {similarity:.2f}")
                context_parts.append(f"Nội dung: {content}")
                context_parts.append("-" * 50)

        return "\n".join(context_parts)

    def ask_gemini_with_rag(self, user_question: str) -> str:
        """Main method to answer questions using RAG"""
        try:
            # Search for relevant content
            search_results = self.search_relevant_content(user_question)

            # Format context from search results
            context = self.format_context(search_results)

            # Build the complete prompt
            if context:
                full_prompt = f"""{self.system_prompt}

{context}

=== CÂU HỎI CỦA NGƯỜI DÙNG ===
{user_question}

=== HƯỚNG DẪN TRẢ LỜI ===
Hãy trả lời câu hỏi dựa trên thông tin từ tài liệu ở trên. Nếu thông tin không đủ, hãy bổ sung từ kiến thức của bạn và ghi rõ nguồn."""
            else:
                full_prompt = f"""{self.system_prompt}

=== CÂU HỎI CỦA NGƯỜI DÙNG ===
{user_question}

=== HƯỚNG DẪN TRẢ LỜI ===
Không tìm thấy thông tin liên quan trong tài liệu đã cung cấp. Hãy trả lời dựa trên kiến thức tổng quát của bạn."""

            # Generate response
            response = self.model.generate_content(full_prompt)

            # Add source information
            if search_results:
                source_info = f"\n\n📚 **Nguồn tham khảo:**\n"
                unique_files = set()
                for result in search_results[:3]:  # Show top 3 sources
                    metadata = result.get("metadata", {})
                    file_name = metadata.get("original_name", "Unknown")
                    if file_name not in unique_files:
                        unique_files.add(file_name)
                        source_info += f"- {file_name}\n"

                return response.text + source_info
            else:
                return response.text + "\n\n💡 *Thông tin này dựa trên kiến thức tổng quát, không có trong tài liệu đã cung cấp.*"

        except Exception as e:
            logger.error(f"Error in ask_gemini_with_rag: {str(e)}")
            return f"Xin lỗi, đã có lỗi xảy ra khi xử lý câu hỏi của bạn: {str(e)}"

    def get_vector_store_stats(self) -> Dict:
        """Get statistics about the vector store"""
        return self.vector_store.get_collection_stats()

# Backward compatibility - keep the original function
def ask_gemini(prompt: str) -> str:
    """Original function for backward compatibility"""
    try:
        agent = GeminiRAGAgent()
        return agent.ask_gemini_with_rag(prompt)
    except Exception as e:
        # Fallback to simple Gemini call
        genai.configure(api_key=os.getenv("GEMINI_API_KEY"))
        model = genai.GenerativeModel("models/gemini-2.0-flash")
        response = model.generate_content(prompt)
        return response.text

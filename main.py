from fastapi import <PERSON><PERSON><PERSON>, Request, Form, UploadFile, File, HTTPException
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import <PERSON><PERSON>2Templates
import os
import asyncio
from typing import List

from gemini_agent import GeminiRAGAgent, ask_gemini
from file_manager import FileManager
from content_extractor import ContentExtractor
from vector_store import VectorStore

app = FastAPI(title="AI Agent with Multi-format File Analysis")

# Mount thư mục static để load CSS
app.mount("/static", StaticFiles(directory="static"), name="static")

# Nơi chứa HTML templates
templates = Jinja2Templates(directory="templates")

# Initialize components
file_manager = FileManager()
content_extractor = ContentExtractor(gemini_api_key=os.getenv("GEMINI_API_KEY"))
vector_store = VectorStore()
rag_agent = GeminiRAGAgent()


@app.get("/", response_class=HTMLResponse)
@app.get("/home", response_class=HTMLResponse)
def get_home(request: Request):
    # Get list of uploaded files
    uploaded_files = file_manager.get_all_files()
    vector_stats = vector_store.get_collection_stats()

    return templates.TemplateResponse("home.html", {
        "request": request,
        "messages": [],
        "uploaded_files": uploaded_files,
        "vector_stats": vector_stats
    })


@app.post("/upload", response_class=JSONResponse)
async def upload_file(file: UploadFile = File(...)):
    """Upload and process a file"""
    try:
        # Check file size (limit to 50MB)
        if file.size > 50 * 1024 * 1024:
            raise HTTPException(status_code=413, detail="File too large. Maximum size is 50MB.")

        # Save uploaded file
        file_metadata = await file_manager.save_uploaded_file(file)

        # Extract content from file
        extraction_result = content_extractor.extract_content(
            file_metadata["file_path"],
            file_metadata["file_type"]
        )

        if extraction_result["success"]:
            # Add to vector store
            vector_metadata = {
                "id": file_metadata["id"],
                "original_name": file_metadata["original_name"],
                "file_type": file_metadata["file_type"],
                "upload_time": file_metadata["upload_time"]
            }

            vector_success = vector_store.add_document(
                file_metadata["id"],
                extraction_result["content"],
                vector_metadata
            )

            # Update file metadata
            file_manager.update_file_metadata(file_metadata["id"], {
                "processed": True,
                "content_extracted": True,
                "vector_stored": vector_success,
                "extraction_metadata": extraction_result.get("metadata", {})
            })

            return JSONResponse({
                "success": True,
                "message": f"File '{file.filename}' uploaded and processed successfully!",
                "file_id": file_metadata["id"],
                "extraction_success": extraction_result["success"],
                "vector_success": vector_success
            })
        else:
            # Update file metadata for failed extraction
            file_manager.update_file_metadata(file_metadata["id"], {
                "processed": True,
                "content_extracted": False,
                "extraction_error": extraction_result.get("error", "Unknown error")
            })

            return JSONResponse({
                "success": False,
                "message": f"File uploaded but content extraction failed: {extraction_result.get('error', 'Unknown error')}",
                "file_id": file_metadata["id"]
            })

    except Exception as e:
        return JSONResponse({
            "success": False,
            "message": f"Error uploading file: {str(e)}"
        }, status_code=500)


@app.post("/chat", response_class=JSONResponse)
async def chat_endpoint(request: Request, user_input: str = Form(...)):
    """Chat endpoint using RAG"""
    try:
        # Use RAG agent to answer
        answer = rag_agent.ask_gemini_with_rag(user_input)

        return JSONResponse({
            "success": True,
            "user_message": user_input,
            "bot_response": answer
        })

    except Exception as e:
        return JSONResponse({
            "success": False,
            "error": f"Error processing chat: {str(e)}"
        }, status_code=500)


@app.post("/home", response_class=HTMLResponse)
async def post_home(request: Request, user_input: str = Form(...)):
    """Legacy endpoint for backward compatibility"""
    try:
        # Use RAG agent to answer
        answer = rag_agent.ask_gemini_with_rag(user_input)

        # Get updated file list and stats
        uploaded_files = file_manager.get_all_files()
        vector_stats = vector_store.get_collection_stats()

        # Create message history
        messages = [
            {"sender": "user", "text": user_input},
            {"sender": "bot", "text": answer}
        ]

        return templates.TemplateResponse("home.html", {
            "request": request,
            "messages": messages,
            "uploaded_files": uploaded_files,
            "vector_stats": vector_stats
        })

    except Exception as e:
        # Fallback to simple response
        messages = [
            {"sender": "user", "text": user_input},
            {"sender": "bot", "text": f"Xin lỗi, đã có lỗi xảy ra: {str(e)}"}
        ]

        return templates.TemplateResponse("home.html", {
            "request": request,
            "messages": messages,
            "uploaded_files": [],
            "vector_stats": {}
        })


@app.get("/files", response_class=JSONResponse)
def get_files():
    """Get list of all uploaded files"""
    try:
        files = file_manager.get_all_files()
        return JSONResponse({"success": True, "files": files})
    except Exception as e:
        return JSONResponse({"success": False, "error": str(e)}, status_code=500)


@app.delete("/files/{file_id}", response_class=JSONResponse)
def delete_file(file_id: str):
    """Delete a file and its vector data"""
    try:
        # Delete from vector store
        vector_store.delete_document(file_id)

        # Delete file and metadata
        success = file_manager.delete_file(file_id)

        if success:
            return JSONResponse({"success": True, "message": "File deleted successfully"})
        else:
            return JSONResponse({"success": False, "message": "File not found"}, status_code=404)

    except Exception as e:
        return JSONResponse({"success": False, "error": str(e)}, status_code=500)


@app.get("/stats", response_class=JSONResponse)
def get_stats():
    """Get system statistics"""
    try:
        vector_stats = vector_store.get_collection_stats()
        file_stats = {
            "total_files": len(file_manager.get_all_files()),
            "processed_files": len([f for f in file_manager.get_all_files() if f.get("processed", False)]),
            "unprocessed_files": len(file_manager.get_unprocessed_files())
        }

        return JSONResponse({
            "success": True,
            "vector_stats": vector_stats,
            "file_stats": file_stats
        })
    except Exception as e:
        return JSONResponse({"success": False, "error": str(e)}, status_code=500)


# Health check endpoint
@app.get("/health")
def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "message": "AI Agent is running"}


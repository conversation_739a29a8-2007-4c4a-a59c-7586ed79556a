import os
import uuid
import logging
from typing import List, Dict, Optional, Tuple
from pathlib import Path
import chromadb
from chromadb.config import Settings
from sentence_transformers import SentenceTransformer
import numpy as np

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VectorStore:
    def __init__(self, persist_directory: str = "chroma_db", collection_name: str = "documents"):
        """Initialize ChromaDB vector store with sentence transformers"""
        self.persist_directory = persist_directory
        self.collection_name = collection_name

        # Initialize ChromaDB client
        self.client = chromadb.PersistentClient(
            path=persist_directory,
            settings=Settings(anonymized_telemetry=False)
        )

        # Initialize embedding model
        self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')

        # Get or create collection
        try:
            self.collection = self.client.get_collection(name=collection_name)
            logger.info(f"Loaded existing collection: {collection_name}")
        except:
            self.collection = self.client.create_collection(
                name=collection_name,
                metadata={"description": "Document content embeddings"}
            )
            logger.info(f"Created new collection: {collection_name}")

    def _chunk_text(self, text: str, chunk_size: int = 1000, overlap: int = 200) -> List[str]:
        """Split text into overlapping chunks for better retrieval"""
        if len(text) <= chunk_size:
            return [text]

        chunks = []
        start = 0

        while start < len(text):
            end = start + chunk_size

            # Try to break at sentence boundary
            if end < len(text):
                # Look for sentence endings
                for i in range(end, max(start + chunk_size - 200, start), -1):
                    if text[i] in '.!?':
                        end = i + 1
                        break

            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)

            start = end - overlap
            if start >= len(text):
                break

        return chunks

    def add_document(self, file_id: str, content: str, metadata: Dict) -> bool:
        """Add document content to vector store"""
        try:
            # Split content into chunks
            chunks = self._chunk_text(content)

            if not chunks:
                logger.warning(f"No content to add for file {file_id}")
                return False

            # Generate embeddings
            embeddings = self.embedding_model.encode(chunks).tolist()

            # Create unique IDs for each chunk
            chunk_ids = [f"{file_id}_chunk_{i}" for i in range(len(chunks))]

            # Prepare metadata for each chunk
            chunk_metadata = []
            for i, chunk in enumerate(chunks):
                chunk_meta = metadata.copy()
                chunk_meta.update({
                    "chunk_index": i,
                    "chunk_count": len(chunks),
                    "chunk_text_preview": chunk[:100] + "..." if len(chunk) > 100 else chunk
                })
                chunk_metadata.append(chunk_meta)

            # Add to collection
            self.collection.add(
                embeddings=embeddings,
                documents=chunks,
                metadatas=chunk_metadata,
                ids=chunk_ids
            )

            logger.info(f"Added {len(chunks)} chunks for file {file_id}")
            return True

        except Exception as e:
            logger.error(f"Error adding document {file_id}: {str(e)}")
            return False

    def search_similar(self, query: str, n_results: int = 5, file_types: Optional[List[str]] = None) -> List[Dict]:
        """Search for similar content based on query"""
        try:
            # Generate query embedding
            query_embedding = self.embedding_model.encode([query]).tolist()

            # Prepare where clause for filtering
            where_clause = {}
            if file_types:
                where_clause["file_type"] = {"$in": file_types}

            # Search in collection
            results = self.collection.query(
                query_embeddings=query_embedding,
                n_results=n_results,
                where=where_clause if where_clause else None,
                include=["documents", "metadatas", "distances"]
            )

            # Format results
            formatted_results = []
            if results["documents"] and results["documents"][0]:
                for i in range(len(results["documents"][0])):
                    formatted_results.append({
                        "content": results["documents"][0][i],
                        "metadata": results["metadatas"][0][i],
                        "similarity_score": 1 - results["distances"][0][i],  # Convert distance to similarity
                        "file_id": results["metadatas"][0][i].get("id", "unknown")
                    })

            logger.info(f"Found {len(formatted_results)} similar documents for query: {query[:50]}...")
            return formatted_results

        except Exception as e:
            logger.error(f"Error searching for query '{query}': {str(e)}")
            return []

    def get_document_chunks(self, file_id: str) -> List[Dict]:
        """Get all chunks for a specific document"""
        try:
            results = self.collection.get(
                where={"id": file_id},
                include=["documents", "metadatas"]
            )

            chunks = []
            if results["documents"]:
                for i, doc in enumerate(results["documents"]):
                    chunks.append({
                        "content": doc,
                        "metadata": results["metadatas"][i]
                    })

            return sorted(chunks, key=lambda x: x["metadata"].get("chunk_index", 0))

        except Exception as e:
            logger.error(f"Error getting chunks for document {file_id}: {str(e)}")
            return []

    def delete_document(self, file_id: str) -> bool:
        """Delete all chunks for a specific document"""
        try:
            # Get all chunk IDs for this document
            results = self.collection.get(
                where={"id": file_id},
                include=["metadatas"]
            )

            if not results["ids"]:
                logger.warning(f"No chunks found for document {file_id}")
                return False

            # Delete all chunks
            self.collection.delete(ids=results["ids"])

            logger.info(f"Deleted {len(results['ids'])} chunks for document {file_id}")
            return True

        except Exception as e:
            logger.error(f"Error deleting document {file_id}: {str(e)}")
            return False

    def get_collection_stats(self) -> Dict:
        """Get statistics about the collection"""
        try:
            count = self.collection.count()

            # Get sample of metadata to analyze file types
            sample_results = self.collection.get(
                limit=min(100, count),
                include=["metadatas"]
            )

            file_types = {}
            unique_files = set()

            for metadata in sample_results["metadatas"]:
                file_type = metadata.get("file_type", "unknown")
                file_types[file_type] = file_types.get(file_type, 0) + 1
                unique_files.add(metadata.get("id", "unknown"))

            return {
                "total_chunks": count,
                "unique_files": len(unique_files),
                "file_types": file_types,
                "collection_name": self.collection_name
            }

        except Exception as e:
            logger.error(f"Error getting collection stats: {str(e)}")
            return {"error": str(e)}

    def update_document(self, file_id: str, new_content: str, new_metadata: Dict) -> bool:
        """Update document by deleting old chunks and adding new ones"""
        try:
            # Delete existing chunks
            self.delete_document(file_id)

            # Add new content
            return self.add_document(file_id, new_content, new_metadata)

        except Exception as e:
            logger.error(f"Error updating document {file_id}: {str(e)}")
            return False
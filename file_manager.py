import os
import uuid
import json
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional
import aiofiles
from fastapi import UploadFile

class FileManager:
    def __init__(self, upload_dir: str = "uploads", metadata_file: str = "file_metadata.json"):
        self.upload_dir = Path(upload_dir)
        self.metadata_file = Path(metadata_file)
        self.upload_dir.mkdir(exist_ok=True)

        # Supported file types
        self.supported_extensions = {
            'documents': ['.docx', '.doc', '.pdf', '.txt'],
            'spreadsheets': ['.xlsx', '.xls', '.csv'],
            'images': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff'],
            'audio': ['.mp3', '.wav', '.m4a', '.flac'],
            'video': ['.mp4', '.avi', '.mov', '.mkv', '.wmv']
        }

    def _load_metadata(self) -> Dict:
        """Load file metadata from JSON file"""
        if self.metadata_file.exists():
            try:
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                return {"files": []}
        return {"files": []}

    def _save_metadata(self, metadata: Dict):
        """Save file metadata to JSON file"""
        with open(self.metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)

    def get_file_type(self, filename: str) -> str:
        """Determine file type based on extension"""
        ext = Path(filename).suffix.lower()
        for file_type, extensions in self.supported_extensions.items():
            if ext in extensions:
                return file_type
        return 'unknown'

    def is_supported_file(self, filename: str) -> bool:
        """Check if file type is supported"""
        return self.get_file_type(filename) != 'unknown'

    async def save_uploaded_file(self, file: UploadFile) -> Dict:
        """Save uploaded file and return metadata"""
        if not self.is_supported_file(file.filename):
            raise ValueError(f"Unsupported file type: {file.filename}")

        # Generate unique filename
        file_id = str(uuid.uuid4())
        file_extension = Path(file.filename).suffix
        unique_filename = f"{file_id}{file_extension}"
        file_path = self.upload_dir / unique_filename

        # Save file
        async with aiofiles.open(file_path, 'wb') as f:
            content = await file.read()
            await f.write(content)

        # Create metadata
        file_metadata = {
            "id": file_id,
            "original_name": file.filename,
            "stored_name": unique_filename,
            "file_path": str(file_path),
            "file_type": self.get_file_type(file.filename),
            "file_size": len(content),
            "upload_time": datetime.now().isoformat(),
            "processed": False,
            "content_extracted": False
        }

        # Update metadata file
        metadata = self._load_metadata()
        metadata["files"].append(file_metadata)
        self._save_metadata(metadata)

        return file_metadata

    def get_all_files(self) -> List[Dict]:
        """Get list of all uploaded files"""
        metadata = self._load_metadata()
        return metadata.get("files", [])

    def get_file_by_id(self, file_id: str) -> Optional[Dict]:
        """Get file metadata by ID"""
        files = self.get_all_files()
        for file_info in files:
            if file_info["id"] == file_id:
                return file_info
        return None

    def update_file_metadata(self, file_id: str, updates: Dict):
        """Update file metadata"""
        metadata = self._load_metadata()
        for file_info in metadata["files"]:
            if file_info["id"] == file_id:
                file_info.update(updates)
                break
        self._save_metadata(metadata)

    def delete_file(self, file_id: str) -> bool:
        """Delete file and its metadata"""
        file_info = self.get_file_by_id(file_id)
        if not file_info:
            return False

        # Delete physical file
        file_path = Path(file_info["file_path"])
        if file_path.exists():
            file_path.unlink()

        # Remove from metadata
        metadata = self._load_metadata()
        metadata["files"] = [f for f in metadata["files"] if f["id"] != file_id]
        self._save_metadata(metadata)

        return True

    def get_files_by_type(self, file_type: str) -> List[Dict]:
        """Get files filtered by type"""
        files = self.get_all_files()
        return [f for f in files if f["file_type"] == file_type]

    def get_unprocessed_files(self) -> List[Dict]:
        """Get files that haven't been processed yet"""
        files = self.get_all_files()
        return [f for f in files if not f.get("processed", False)]
import os
import io
import logging
from pathlib import Path
from typing import Dict, List, Optional

# Document processing
from docx import Document
import openpyxl
import PyPDF2
import pdfplumber

# Image processing
from PIL import Image
import google.generativeai as genai

# Audio processing
import speech_recognition as sr
from pydub import AudioSegment

# Video processing
import cv2
from moviepy.editor import VideoFileClip

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ContentExtractor:
    def __init__(self, gemini_api_key: str):
        """Initialize content extractor with Gemini API for image analysis"""
        genai.configure(api_key=gemini_api_key)
        self.vision_model = genai.GenerativeModel('gemini-2.0-flash')
        self.recognizer = sr.Recognizer()

    def extract_text_from_word(self, file_path: str) -> Dict[str, str]:
        """Extract text from Word documents (.docx)"""
        try:
            doc = Document(file_path)
            text_content = []

            # Extract paragraphs
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text.strip())

            # Extract tables
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        text_content.append(" | ".join(row_text))

            extracted_text = "\n".join(text_content)

            return {
                "success": True,
                "content": extracted_text,
                "metadata": {
                    "paragraphs": len(doc.paragraphs),
                    "tables": len(doc.tables),
                    "word_count": len(extracted_text.split())
                }
            }
        except Exception as e:
            logger.error(f"Error extracting from Word file {file_path}: {str(e)}")
            return {"success": False, "error": str(e), "content": ""}

    def extract_text_from_excel(self, file_path: str) -> Dict[str, str]:
        """Extract text from Excel files (.xlsx, .xls)"""
        try:
            workbook = openpyxl.load_workbook(file_path, data_only=True)
            text_content = []

            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                text_content.append(f"=== Sheet: {sheet_name} ===")

                for row in sheet.iter_rows(values_only=True):
                    row_text = []
                    for cell in row:
                        if cell is not None and str(cell).strip():
                            row_text.append(str(cell).strip())
                    if row_text:
                        text_content.append(" | ".join(row_text))

            extracted_text = "\n".join(text_content)

            return {
                "success": True,
                "content": extracted_text,
                "metadata": {
                    "sheets": len(workbook.sheetnames),
                    "sheet_names": workbook.sheetnames,
                    "word_count": len(extracted_text.split())
                }
            }
        except Exception as e:
            logger.error(f"Error extracting from Excel file {file_path}: {str(e)}")
            return {"success": False, "error": str(e), "content": ""}

    def extract_text_from_pdf(self, file_path: str) -> Dict[str, str]:
        """Extract text from PDF files"""
        try:
            text_content = []

            # Try with pdfplumber first (better for complex layouts)
            with pdfplumber.open(file_path) as pdf:
                for page_num, page in enumerate(pdf.pages, 1):
                    text = page.extract_text()
                    if text and text.strip():
                        text_content.append(f"=== Page {page_num} ===")
                        text_content.append(text.strip())

            # If pdfplumber fails, try PyPDF2
            if not text_content:
                with open(file_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    for page_num, page in enumerate(pdf_reader.pages, 1):
                        text = page.extract_text()
                        if text and text.strip():
                            text_content.append(f"=== Page {page_num} ===")
                            text_content.append(text.strip())

            extracted_text = "\n".join(text_content)

            return {
                "success": True,
                "content": extracted_text,
                "metadata": {
                    "pages": len(text_content) // 2 if text_content else 0,
                    "word_count": len(extracted_text.split())
                }
            }
        except Exception as e:
            logger.error(f"Error extracting from PDF file {file_path}: {str(e)}")
            return {"success": False, "error": str(e), "content": ""}

    def extract_text_from_image(self, file_path: str) -> Dict[str, str]:
        """Extract text from images using Gemini Vision"""
        try:
            # Upload image to Gemini
            with open(file_path, 'rb') as f:
                image_data = f.read()

            # Create image part for Gemini
            image_part = {
                "mime_type": "image/jpeg",  # Adjust based on actual image type
                "data": image_data
            }

            prompt = """Analyze this image and extract all text content.
            Also describe what you see in the image.
            Format your response as:
            TEXT CONTENT:
            [extracted text here]

            IMAGE DESCRIPTION:
            [description of what's in the image]"""

            response = self.vision_model.generate_content([prompt, image_part])

            return {
                "success": True,
                "content": response.text,
                "metadata": {
                    "image_size": os.path.getsize(file_path),
                    "extraction_method": "gemini_vision"
                }
            }
        except Exception as e:
            logger.error(f"Error extracting from image file {file_path}: {str(e)}")
            return {"success": False, "error": str(e), "content": ""}

    def extract_text_from_audio(self, file_path: str) -> Dict[str, str]:
        """Extract text from audio files using speech recognition"""
        try:
            # Convert audio to WAV format if needed
            audio = AudioSegment.from_file(file_path)

            # Convert to WAV in memory
            wav_io = io.BytesIO()
            audio.export(wav_io, format="wav")
            wav_io.seek(0)

            # Use speech recognition
            with sr.AudioFile(wav_io) as source:
                audio_data = self.recognizer.record(source)
                text = self.recognizer.recognize_google(audio_data, language='vi-VN')

            return {
                "success": True,
                "content": text,
                "metadata": {
                    "duration_seconds": len(audio) / 1000,
                    "sample_rate": audio.frame_rate,
                    "channels": audio.channels,
                    "extraction_method": "google_speech_recognition"
                }
            }
        except sr.UnknownValueError:
            return {"success": False, "error": "Could not understand audio", "content": ""}
        except sr.RequestError as e:
            logger.error(f"Speech recognition service error: {str(e)}")
            return {"success": False, "error": f"Speech recognition service error: {str(e)}", "content": ""}
        except Exception as e:
            logger.error(f"Error extracting from audio file {file_path}: {str(e)}")
            return {"success": False, "error": str(e), "content": ""}

    def extract_text_from_video(self, file_path: str) -> Dict[str, str]:
        """Extract text from video files (audio track + key frames)"""
        try:
            # Extract audio from video
            video = VideoFileClip(file_path)

            # Extract audio and save temporarily
            temp_audio_path = f"temp_audio_{os.path.basename(file_path)}.wav"
            video.audio.write_audiofile(temp_audio_path, verbose=False, logger=None)

            # Extract text from audio
            audio_result = self.extract_text_from_audio(temp_audio_path)

            # Clean up temporary audio file
            if os.path.exists(temp_audio_path):
                os.remove(temp_audio_path)

            # Extract key frames for visual analysis (optional)
            frame_descriptions = []
            duration = video.duration

            # Extract frames at 10-second intervals
            for t in range(0, int(duration), 10):
                try:
                    frame = video.get_frame(t)
                    # Save frame temporarily
                    temp_frame_path = f"temp_frame_{t}.jpg"
                    Image.fromarray(frame.astype('uint8')).save(temp_frame_path)

                    # Analyze frame with Gemini Vision
                    frame_result = self.extract_text_from_image(temp_frame_path)
                    if frame_result["success"]:
                        frame_descriptions.append(f"Frame at {t}s: {frame_result['content']}")

                    # Clean up temporary frame
                    if os.path.exists(temp_frame_path):
                        os.remove(temp_frame_path)
                except:
                    continue

            video.close()

            # Combine audio and visual content
            content_parts = []
            if audio_result["success"]:
                content_parts.append(f"AUDIO TRANSCRIPT:\n{audio_result['content']}")

            if frame_descriptions:
                content_parts.append(f"VISUAL CONTENT:\n" + "\n".join(frame_descriptions))

            combined_content = "\n\n".join(content_parts)

            return {
                "success": True,
                "content": combined_content,
                "metadata": {
                    "duration_seconds": duration,
                    "audio_extracted": audio_result["success"],
                    "frames_analyzed": len(frame_descriptions),
                    "extraction_method": "video_multimodal"
                }
            }
        except Exception as e:
            logger.error(f"Error extracting from video file {file_path}: {str(e)}")
            return {"success": False, "error": str(e), "content": ""}

    def extract_content(self, file_path: str, file_type: str) -> Dict[str, str]:
        """Main method to extract content based on file type"""
        file_path = Path(file_path)

        if not file_path.exists():
            return {"success": False, "error": "File not found", "content": ""}

        logger.info(f"Extracting content from {file_path} (type: {file_type})")

        try:
            if file_type == "documents":
                if file_path.suffix.lower() in ['.docx', '.doc']:
                    return self.extract_text_from_word(str(file_path))
                elif file_path.suffix.lower() == '.pdf':
                    return self.extract_text_from_pdf(str(file_path))
                elif file_path.suffix.lower() == '.txt':
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    return {
                        "success": True,
                        "content": content,
                        "metadata": {"word_count": len(content.split())}
                    }

            elif file_type == "spreadsheets":
                return self.extract_text_from_excel(str(file_path))

            elif file_type == "images":
                return self.extract_text_from_image(str(file_path))

            elif file_type == "audio":
                return self.extract_text_from_audio(str(file_path))

            elif file_type == "video":
                return self.extract_text_from_video(str(file_path))

            else:
                return {"success": False, "error": f"Unsupported file type: {file_type}", "content": ""}

        except Exception as e:
            logger.error(f"Error in extract_content for {file_path}: {str(e)}")
            return {"success": False, "error": str(e), "content": ""}
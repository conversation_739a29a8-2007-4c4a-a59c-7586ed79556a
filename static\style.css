/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  padding: 20px;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
}

/* Header */
.header {
  text-align: center;
  color: white;
  margin-bottom: 30px;
}

.header h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

/* Main Content Layout */
.main-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 30px;
  height: calc(100vh - 200px);
}

/* Left Panel */
.left-panel {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  overflow-y: auto;
}

.left-panel h3 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.3rem;
  border-bottom: 2px solid #667eea;
  padding-bottom: 10px;
}

/* Upload Section */
.upload-section {
  margin-bottom: 30px;
}

.upload-area {
  border: 3px dashed #667eea;
  border-radius: 10px;
  padding: 30px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f8f9ff;
}

.upload-area:hover, .upload-area.dragover {
  border-color: #764ba2;
  background: #f0f2ff;
  transform: translateY(-2px);
}

.upload-area input[type="file"] {
  display: none;
}

.upload-text p {
  font-size: 1.1rem;
  color: #667eea;
  margin-bottom: 5px;
}

.upload-text small {
  color: #666;
}

#uploadStatus {
  margin-top: 15px;
}

.uploading {
  color: #ff9800;
  font-weight: bold;
}

.success {
  color: #4caf50;
  font-weight: bold;
}

.error {
  color: #f44336;
  font-weight: bold;
}

/* File List */
.file-list-section {
  margin-bottom: 30px;
}

.file-list {
  max-height: 300px;
  overflow-y: auto;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  margin-bottom: 10px;
  background: #f8f9ff;
  border-radius: 8px;
  border-left: 4px solid #667eea;
  transition: all 0.3s ease;
}

.file-item:hover {
  background: #f0f2ff;
  transform: translateX(5px);
}

.file-info {
  flex: 1;
}

.file-name {
  display: block;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.file-type {
  display: inline-block;
  background: #667eea;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  margin-right: 10px;
}

.file-status {
  font-size: 0.9rem;
  color: #666;
}

.delete-btn {
  background: #f44336;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 5px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.delete-btn:hover {
  background: #d32f2f;
}

.no-files {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 20px;
}

/* Statistics */
.stats-section {
  background: #f8f9ff;
  padding: 20px;
  border-radius: 10px;
  border: 1px solid #e0e7ff;
}

.stats {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  color: #666;
}

.stat-value {
  font-weight: bold;
  color: #667eea;
  background: white;
  padding: 5px 10px;
  border-radius: 15px;
}

/* Right Panel - Chat */
.right-panel {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.chat-container h3 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.3rem;
  border-bottom: 2px solid #667eea;
  padding-bottom: 10px;
}

.chat-box {
  flex: 1;
  overflow-y: auto;
  border: 1px solid #e0e7ff;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 20px;
  background: #f8f9ff;
  max-height: calc(100vh - 400px);
}

.message {
  margin-bottom: 20px;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.message.user {
  text-align: right;
}

.message.bot {
  text-align: left;
}

.message-content {
  display: inline-block;
  max-width: 80%;
  padding: 15px 20px;
  border-radius: 20px;
  word-wrap: break-word;
}

.message.user .message-content {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-bottom-right-radius: 5px;
}

.message.bot .message-content {
  background: #f0f2ff;
  color: #333;
  border: 1px solid #e0e7ff;
  border-bottom-left-radius: 5px;
}

.message-text {
  margin-top: 8px;
  line-height: 1.5;
  white-space: pre-wrap;
}

/* Chat Form */
.chat-form {
  margin-top: auto;
}

.input-group {
  display: flex;
  gap: 10px;
  align-items: center;
}

input[name="user_input"] {
  flex: 1;
  padding: 15px 20px;
  border: 2px solid #e0e7ff;
  border-radius: 25px;
  font-size: 1rem;
  outline: none;
  transition: border-color 0.3s ease;
}

input[name="user_input"]:focus {
  border-color: #667eea;
}

#sendBtn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 15px 25px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: bold;
  transition: all 0.3s ease;
}

#sendBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .main-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .header h1 {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  body {
    padding: 10px;
  }

  .left-panel, .right-panel {
    padding: 20px;
  }

  .header h1 {
    font-size: 1.8rem;
  }

  .input-group {
    flex-direction: column;
  }

  input[name="user_input"] {
    margin-bottom: 10px;
  }
}

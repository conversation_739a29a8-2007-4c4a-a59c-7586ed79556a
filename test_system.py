#!/usr/bin/env python3
"""
Test script for AI Agent system
"""

import os
import sys
import asyncio
from pathlib import Path

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

def test_imports():
    """Test if all required modules can be imported"""
    print("🔍 Testing imports...")

    try:
        import fastapi
        print("✅ FastAPI imported successfully")
    except ImportError as e:
        print(f"❌ FastAPI import failed: {e}")
        return False

    try:
        import google.generativeai as genai
        print("✅ Google Generative AI imported successfully")
    except ImportError as e:
        print(f"❌ Google Generative AI import failed: {e}")
        return False

    try:
        import chromadb
        print("✅ ChromaDB imported successfully")
    except ImportError as e:
        print(f"❌ ChromaDB import failed: {e}")
        return False

    try:
        from sentence_transformers import SentenceTransformer
        print("✅ Sentence Transformers imported successfully")
    except ImportError as e:
        print(f"❌ Sentence Transformers import failed: {e}")
        return False

    return True

def test_env_config():
    """Test environment configuration"""
    print("\n🔧 Testing environment configuration...")

    from dotenv import load_dotenv
    load_dotenv()

    api_key = os.getenv("GEMINI_API_KEY")
    if api_key and api_key != "your_gemini_api_key_here":
        print("✅ GEMINI_API_KEY configured")
        return True
    else:
        print("❌ GEMINI_API_KEY not configured or using default value")
        print("   Please set your API key in .env file")
        return False

def test_modules():
    """Test custom modules"""
    print("\n📦 Testing custom modules...")

    try:
        from file_manager import FileManager
        fm = FileManager()
        print("✅ FileManager initialized successfully")
    except Exception as e:
        print(f"❌ FileManager failed: {e}")
        return False

    try:
        from vector_store import VectorStore
        vs = VectorStore()
        print("✅ VectorStore initialized successfully")
    except Exception as e:
        print(f"❌ VectorStore failed: {e}")
        return False

    try:
        from content_extractor import ContentExtractor
        # Skip initialization as it requires API key
        print("✅ ContentExtractor module imported successfully")
    except Exception as e:
        print(f"❌ ContentExtractor failed: {e}")
        return False

    try:
        from gemini_agent import GeminiRAGAgent
        # Skip initialization as it requires API key
        print("✅ GeminiRAGAgent module imported successfully")
    except Exception as e:
        print(f"❌ GeminiRAGAgent failed: {e}")
        return False

    return True

def test_directories():
    """Test required directories"""
    print("\n📁 Testing directories...")

    required_dirs = ["templates", "static", "uploads"]
    all_good = True

    for dir_name in required_dirs:
        if Path(dir_name).exists():
            print(f"✅ {dir_name}/ directory exists")
        else:
            print(f"❌ {dir_name}/ directory missing")
            all_good = False

    return all_good

def main():
    """Run all tests"""
    print("🚀 Starting AI Agent System Tests\n")

    tests = [
        ("Import Test", test_imports),
        ("Environment Config", test_env_config),
        ("Custom Modules", test_modules),
        ("Directory Structure", test_directories)
    ]

    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))

    # Summary
    print("\n" + "="*50)
    print("📊 TEST SUMMARY")
    print("="*50)

    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1

    print(f"\nResults: {passed}/{len(results)} tests passed")

    if passed == len(results):
        print("\n🎉 All tests passed! System is ready to run.")
        print("Run: uvicorn main:app --reload")
    else:
        print("\n⚠️  Some tests failed. Please fix the issues before running.")
        print("Check README.md for installation instructions.")

if __name__ == "__main__":
    main()
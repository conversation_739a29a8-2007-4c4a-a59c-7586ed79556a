from fastapi import <PERSON><PERSON><PERSON>, Request, Form
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates

from gemini_agent import ask_gemini

app = FastAPI()

# Mount thư mục static để load CSS
app.mount("/static", StaticFiles(directory="static"), name="static")

# N<PERSON>i chứa HTML templates
templates = Jinja2Templates(directory="templates")


@app.get("/home", response_class=HTMLResponse)
def get_home(request: Request):
    return templates.TemplateResponse("home.html", {"request": request, "messages": []})


@app.post("/home", response_class=HTMLResponse)
async def post_home(request: Request, user_input: str = Form(...)):
    # <PERSON><PERSON><PERSON> câu hỏi đến Gemini
    answer = ask_gemini(user_input)
    
    # Tạo danh sách tin nhắn giả lập (chat history)
    messages = [
        {"sender": "user", "text": user_input},
        {"sender": "bot", "text": answer}
    ]
    
    return templates.TemplateResponse("home.html", {"request": request, "messages": messages})

